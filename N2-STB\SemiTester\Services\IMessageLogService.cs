using Secs4Net;

namespace SemiTester.Services;

/// <summary>
/// 消息日志服务接口
/// </summary>
public interface IMessageLogService
{
    /// <summary>
    /// 消息日志事件
    /// </summary>
    event EventHandler<MessageLogEventArgs>? MessageLogged;

    /// <summary>
    /// 记录发送的消息
    /// </summary>
    void LogSentMessage(SecsMessage message, bool success, TimeSpan? responseTime = null);

    /// <summary>
    /// 记录接收的消息
    /// </summary>
    void LogReceivedMessage(SecsMessage message, bool isPrimary = true);

    /// <summary>
    /// 记录错误
    /// </summary>
    void LogError(string message, Exception? exception = null);

    /// <summary>
    /// 记录信息
    /// </summary>
    void LogInfo(string message);

    /// <summary>
    /// 获取消息历史
    /// </summary>
    IEnumerable<MessageLogEntry> GetMessageHistory(int maxCount = 1000);

    /// <summary>
    /// 清除消息历史
    /// </summary>
    void ClearHistory();

    /// <summary>
    /// 导出消息历史到文件
    /// </summary>
    Task<bool> ExportHistoryAsync(string filePath);
}

/// <summary>
/// 消息日志条目
/// </summary>
public class MessageLogEntry
{
    public DateTime Timestamp { get; set; }
    public MessageDirection Direction { get; set; }
    public string MessageType { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public bool Success { get; set; } = true;
    public TimeSpan? ResponseTime { get; set; }
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 消息方向
/// </summary>
public enum MessageDirection
{
    Sent,
    Received,
    Error,
    Info
}

/// <summary>
/// 消息日志事件参数
/// </summary>
public class MessageLogEventArgs : EventArgs
{
    public MessageLogEntry LogEntry { get; }

    public MessageLogEventArgs(MessageLogEntry logEntry)
    {
        LogEntry = logEntry;
    }
}
