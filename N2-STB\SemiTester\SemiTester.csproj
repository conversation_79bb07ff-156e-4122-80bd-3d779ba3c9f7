<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <AssemblyTitle>SEMI Protocol Tester</AssemblyTitle>
    <AssemblyDescription>Professional SEMI/GEM Protocol Testing Tool</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <Company>STKC</Company>
    <Product>SEMI Tester</Product>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Secs4Net" Version="2.4.4" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="8.0.2" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Proj.API\Proj.API.csproj" />
    <ProjectReference Include="..\Proj.Log\Proj.Log.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Config\" />
    <Folder Include="Logs\" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Config\**">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
