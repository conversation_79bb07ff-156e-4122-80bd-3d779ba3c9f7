namespace SemiTester.Services;

/// <summary>
/// 配置管理器接口
/// </summary>
public interface IConfigurationManager
{
    /// <summary>
    /// 加载配置
    /// </summary>
    Task<SemiTesterConfig> LoadConfigAsync();

    /// <summary>
    /// 保存配置
    /// </summary>
    Task<bool> SaveConfigAsync(SemiTesterConfig config);

    /// <summary>
    /// 获取默认配置
    /// </summary>
    SemiTesterConfig GetDefaultConfig();
}

/// <summary>
/// SEMI测试器配置
/// </summary>
public class SemiTesterConfig
{
    /// <summary>
    /// 连接配置
    /// </summary>
    public SemiConnectionConfig Connection { get; set; } = new();

    /// <summary>
    /// 设备信息
    /// </summary>
    public DeviceInfo Device { get; set; } = new();

    /// <summary>
    /// UI配置
    /// </summary>
    public UIConfig UI { get; set; } = new();

    /// <summary>
    /// 日志配置
    /// </summary>
    public LogConfig Logging { get; set; } = new();
}

/// <summary>
/// 设备信息
/// </summary>
public class DeviceInfo
{
    public string MDLN { get; set; } = "SEMI_TESTER";
    public string SoftRev { get; set; } = "V1.0.0";
    public string Description { get; set; } = "SEMI Protocol Tester";
}

/// <summary>
/// UI配置
/// </summary>
public class UIConfig
{
    public bool AutoScroll { get; set; } = true;
    public int MaxLogEntries { get; set; } = 1000;
    public bool ShowTimestamp { get; set; } = true;
    public bool ShowMessageDetails { get; set; } = true;
    public string Theme { get; set; } = "Default";
}

/// <summary>
/// 日志配置
/// </summary>
public class LogConfig
{
    public bool EnableFileLogging { get; set; } = true;
    public string LogDirectory { get; set; } = "Logs";
    public int MaxLogFiles { get; set; } = 10;
    public long MaxLogFileSize { get; set; } = 10 * 1024 * 1024; // 10MB
}
