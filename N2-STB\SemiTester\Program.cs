using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Proj.API;
using Proj.API.Services;
using SemiTester.Services;
using SemiTester.Forms;

namespace SemiTester;

internal static class Program
{
    [STAThread]
    static void Main()
    {
        Application.EnableVisualStyles();
        Application.SetCompatibleTextRenderingDefault(false);

        // 创建主机构建器
        var builder = Host.CreateApplicationBuilder();

        // 配置服务
        ConfigureServices(builder.Services);

        // 构建主机
        var host = builder.Build();

        // 启动应用程序
        var serviceProvider = host.Services;
        
        try
        {
            var mainForm = serviceProvider.GetRequiredService<MainForm>();
            Application.Run(mainForm);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"应用程序启动失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private static void ConfigureServices(IServiceCollection services)
    {
        // 日志服务
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.AddDebug();
            builder.SetMinimumLevel(LogLevel.Information);
        });

        // SEMI/GEM 相关服务
        services.AddSingleton<ISecsGemConfigService, SecsGemConfigService>();
        services.AddSingleton<ISecsConnectionFactory, SecsConnectionFactory>();
        
        // 应用服务
        services.AddSingleton<ISemiProtocolService, SemiProtocolService>();
        services.AddSingleton<IMessageLogService, MessageLogService>();
        services.AddSingleton<IConfigurationManager, ConfigurationManager>();

        // 窗体
        services.AddTransient<MainForm>();
    }
}
