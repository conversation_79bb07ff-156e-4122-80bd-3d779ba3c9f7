using Proj.API;
using Secs4Net;

namespace SemiTester.Services;

/// <summary>
/// SEMI协议服务接口
/// </summary>
public interface ISemiProtocolService : IDisposable
{
    /// <summary>
    /// 连接状态
    /// </summary>
    bool IsConnected { get; }

    /// <summary>
    /// 通信状态
    /// </summary>
    int CommunicationState { get; }

    /// <summary>
    /// 控制状态
    /// </summary>
    int ControlState { get; }

    /// <summary>
    /// 设备型号
    /// </summary>
    string MDLN { get; set; }

    /// <summary>
    /// 软件版本
    /// </summary>
    string SoftRev { get; set; }

    /// <summary>
    /// 连接配置
    /// </summary>
    SemiConnectionConfig ConnectionConfig { get; set; }

    /// <summary>
    /// 连接状态变化事件
    /// </summary>
    event EventHandler<ConnectionStateChangedEventArgs>? ConnectionStateChanged;

    /// <summary>
    /// 消息接收事件
    /// </summary>
    event EventHandler<MessageReceivedEventArgs>? MessageReceived;

    /// <summary>
    /// 消息发送事件
    /// </summary>
    event EventHandler<MessageSentEventArgs>? MessageSent;

    /// <summary>
    /// 错误事件
    /// </summary>
    event EventHandler<ErrorEventArgs>? ErrorOccurred;

    /// <summary>
    /// 连接到设备
    /// </summary>
    Task<bool> ConnectAsync();

    /// <summary>
    /// 断开连接
    /// </summary>
    Task DisconnectAsync();

    /// <summary>
    /// 发送消息
    /// </summary>
    Task<SecsMessage?> SendMessageAsync(byte stream, byte function, Item? data = null, int timeoutSeconds = 10);

    /// <summary>
    /// 建立通信
    /// </summary>
    Task<bool> EstablishCommunicationAsync();

    /// <summary>
    /// 请求上线
    /// </summary>
    Task<bool> RequestOnlineAsync();

    /// <summary>
    /// 请求下线
    /// </summary>
    Task<bool> RequestOfflineAsync();

    /// <summary>
    /// 切换到本地模式
    /// </summary>
    Task<bool> SwitchToLocalAsync();

    /// <summary>
    /// 切换到远程模式
    /// </summary>
    Task<bool> SwitchToRemoteAsync();

    /// <summary>
    /// 获取设备状态
    /// </summary>
    Task<(int communicationState, int controlState)?> GetEquipmentStatusAsync();

    /// <summary>
    /// 获取当前时间
    /// </summary>
    Task<DateTime?> GetDateTimeAsync();

    /// <summary>
    /// 设置时间
    /// </summary>
    Task<bool> SetDateTimeAsync(DateTime dateTime);

    /// <summary>
    /// 发送Are You There测试
    /// </summary>
    Task<bool> SendAreYouThereAsync();
}

/// <summary>
/// SEMI连接配置
/// </summary>
public class SemiConnectionConfig
{
    public string IpAddress { get; set; } = "127.0.0.1";
    public int Port { get; set; } = 5000;
    public ushort DeviceId { get; set; } = 1;
    public bool IsActive { get; set; } = true;
    public int T3Timeout { get; set; } = 45000;
    public int T5Timeout { get; set; } = 10000;
    public int T6Timeout { get; set; } = 5000;
    public int T7Timeout { get; set; } = 10000;
    public int T8Timeout { get; set; } = 5000;
}

/// <summary>
/// 连接状态变化事件参数
/// </summary>
public class ConnectionStateChangedEventArgs : EventArgs
{
    public ConnectionState NewState { get; }
    public ConnectionState OldState { get; }
    public DateTime Timestamp { get; }

    public ConnectionStateChangedEventArgs(ConnectionState newState, ConnectionState oldState)
    {
        NewState = newState;
        OldState = oldState;
        Timestamp = DateTime.Now;
    }
}

/// <summary>
/// 消息接收事件参数
/// </summary>
public class MessageReceivedEventArgs : EventArgs
{
    public SecsMessage Message { get; }
    public DateTime Timestamp { get; }
    public bool IsPrimary { get; }

    public MessageReceivedEventArgs(SecsMessage message, bool isPrimary = true)
    {
        Message = message;
        IsPrimary = isPrimary;
        Timestamp = DateTime.Now;
    }
}

/// <summary>
/// 消息发送事件参数
/// </summary>
public class MessageSentEventArgs : EventArgs
{
    public SecsMessage Message { get; }
    public DateTime Timestamp { get; }
    public bool Success { get; }

    public MessageSentEventArgs(SecsMessage message, bool success = true)
    {
        Message = message;
        Success = success;
        Timestamp = DateTime.Now;
    }
}

/// <summary>
/// 错误事件参数
/// </summary>
public class ErrorEventArgs : EventArgs
{
    public string Message { get; }
    public Exception? Exception { get; }
    public DateTime Timestamp { get; }

    public ErrorEventArgs(string message, Exception? exception = null)
    {
        Message = message;
        Exception = exception;
        Timestamp = DateTime.Now;
    }
}
