# 增强版 SEMI Protocol Tester 操作文档

## 📋 概述

增强版SemiTester是一个功能完整的SEMI/GEM协议测试工具，**完全移植了N2-STB项目中EnhancedGemEquipment的所有命令响应功能**。当客户端（Host）发送SEMI命令时，测试工具能够**自动响应**，无需手动操作。

### 🎯 主要特性

- ✅ **完整的命令自动响应**：支持S1F13、S1F17、S2F33、S2F35、S2F37、S2F41等所有主要SEMI命令
- ✅ **端口状态模拟**：支持4个端口的状态管理和FOUP操作模拟
- ✅ **事件报告发送**：自动发送S6F11事件报告到Host
- ✅ **实时日志记录**：详细记录所有SEMI消息交互
- ✅ **可视化界面**：直观的端口状态显示和操作控制

## 🚀 快速开始

### 1. 启动应用程序

```bash
cd N2-STB/SemiTester
dotnet run
```

### 2. 配置连接参数

在**连接设置**页面：
- **IP地址**：设置为Host的IP地址（如：*************）
- **端口**：通常为5555（SEMI标准端口）
- **设备ID**：设置为1
- **连接模式**：
  - ✅ **被动模式**（推荐）：作为设备端等待Host连接
  - ⚠️ **主动模式**：主动连接到Host

### 3. 建立连接

1. 点击**连接**按钮
2. 等待状态栏显示"已连接"
3. 观察消息日志中的连接信息

## 📖 详细操作指南

### 🔗 连接管理

#### 连接设置页面
- **IP地址**：Host系统的IP地址
- **端口号**：SEMI通信端口（默认5555）
- **设备ID**：设备标识符（通常为1）
- **主动模式**：是否主动连接Host

#### 连接状态指示
- 🔴 **未连接**：红色状态，等待连接
- 🟡 **连接中**：黄色状态，正在建立连接
- 🟢 **已连接**：绿色状态，连接正常

### 📨 自动消息响应

测试工具会自动响应以下SEMI命令：

#### 基本通信命令
- **S1F1** → S1F2：Are You There（设备存活检测）
- **S1F3** → S1F4：Selected Equipment Status（设备状态查询）
- **S1F11** → S1F12：Status Variable Namelist（状态变量列表）
- **S1F13** → S1F14：Establish Communications（建立通信）
- **S1F15** → S1F16：Request OFF-LINE（请求离线）
- **S1F17** → S1F18：Request ON-LINE（请求在线）

#### 报告管理命令
- **S2F33** → S2F34：Define Report（定义报告）
- **S2F35** → S2F36：Link Event Report（链接事件报告）
- **S2F37** → S2F38：Enable/Disable Event Report（启用/禁用事件报告）

#### 远程控制命令
- **S2F41** → S2F42：Host Command Send（主机命令发送）

#### 其他命令
- **S2F31** → S2F32：Date and Time Request（日期时间请求）

### 🎮 端口控制功能

#### 端口状态管理

在**端口控制**页面可以：

1. **选择端口**：从下拉列表选择Port 1-4
2. **设置状态**：选择端口状态
   - `IDLE`：空闲状态
   - `FOUP_PLACED`：FOUP已放置
   - `PURGING`：正在充气
   - `PURGE_COMPLETED`：充气完成
   - `READY_TO_UNLOAD`：准备卸载
   - `READY_TO_LOAD`：准备装载

3. **设置FOUP ID**：输入FOUP标识符
4. **应用设置**：点击"设置端口状态"按钮

#### 事件模拟功能

##### 🔄 FOUP放置模拟
```
操作：点击"模拟FOUP放置"按钮
效果：
- 设置端口状态为FOUP_PLACED
- 发送事件报告到Host（S6F11）
- 更新端口状态显示
```

##### 🔄 FOUP移除模拟
```
操作：点击"模拟FOUP移除"按钮
效果：
- 设置端口状态为FOUP_REMOVED
- 清空FOUP ID
- 发送事件报告到Host（S6F11）
```

##### 🔄 充气完成模拟
```
操作：点击"模拟充气完成"按钮
效果：
- 设置端口状态为PURGE_COMPLETED
- 发送事件报告到Host（S6F11）
- 更新端口状态显示
```

### 📊 实时状态监控

#### 端口状态表格显示
- **端口**：端口编号（1-4）
- **状态**：当前端口状态
- **FOUP ID**：当前FOUP标识符
- **温度**：模拟温度值（°C）
- **压力**：模拟压力值（Pa）
- **流量**：模拟流量值（L/min）
- **湿度**：模拟湿度值（%）
- **更新时间**：最后更新时间

### 📝 消息日志功能

#### 日志类型
- 🟢 **发送消息**：绿色显示，发送给Host的消息
- 🔵 **接收消息**：蓝色显示，从Host接收的消息
- 🔴 **错误信息**：红色显示，错误和异常信息
- ⚪ **系统信息**：白色显示，系统状态信息

#### 日志操作
- **自动滚动**：勾选后自动滚动到最新消息
- **清空日志**：清除所有日志记录
- **导出日志**：将日志导出为JSON文件

## 🔧 高级功能

### 主机命令响应（S2F41处理）

测试工具支持以下主机命令：

#### PORT_READY_TO_UNLOAD
```json
命令格式：
{
  "command": "PORT_READY_TO_UNLOAD",
  "parameters": {
    "PortNumber": "1"
  }
}

响应：设置指定端口为待卸载状态
```

#### PORT_READY_TO_LOAD
```json
命令格式：
{
  "command": "PORT_READY_TO_LOAD", 
  "parameters": {
    "PortNumber": "1"
  }
}

响应：设置指定端口为待装载状态
```

#### GET_PORT_STATUS
```json
命令格式：
{
  "command": "GET_PORT_STATUS",
  "parameters": {
    "PortNumber": "1"
  }
}

响应：返回指定端口的详细状态信息
```

#### SET_PORT_STATUS
```json
命令格式：
{
  "command": "SET_PORT_STATUS",
  "parameters": {
    "PortNumber": "1",
    "Status": "PURGING",
    "FoupId": "FOUP_001"
  }
}

响应：设置指定端口的状态和FOUP ID
```

#### START_PURGE / STOP_PURGE
```json
开始充气：
{
  "command": "START_PURGE",
  "parameters": {
    "PortNumber": "1"
  }
}

停止充气：
{
  "command": "STOP_PURGE", 
  "parameters": {
    "PortNumber": "1"
  }
}
```

### 事件报告配置

#### 报告定义（S2F33）
Host可以通过S2F33命令定义自定义报告：
```
- 报告ID：唯一标识符
- 变量列表：要包含在报告中的变量ID列表
- 自动响应：测试工具自动确认报告定义
```

#### 事件链接（S2F35）
Host可以通过S2F35命令将事件与报告关联：
```
- 事件ID：事件唯一标识符
- 报告ID列表：与事件关联的报告列表
- 自动响应：测试工具自动确认事件链接
```

#### 事件启用（S2F37）
Host可以通过S2F37命令启用或禁用事件：
```
- 全局控制：启用/禁用所有事件
- 单独控制：启用/禁用特定事件
- 自动响应：测试工具自动确认事件状态变更
```

## 🛠️ 故障排除

### 常见问题

#### 1. 连接失败
**症状**：点击连接后状态仍显示"未连接"
**解决方案**：
- 检查IP地址和端口号是否正确
- 确认Host系统是否已启动并监听指定端口
- 检查网络连接和防火墙设置
- 查看消息日志中的错误信息

#### 2. 消息无响应
**症状**：Host发送消息但测试工具无响应
**解决方案**：
- 检查连接状态是否正常
- 查看消息日志确认是否收到消息
- 检查消息格式是否符合SEMI标准
- 重启测试工具并重新连接

#### 3. 端口状态不更新
**症状**：设置端口状态后显示不更新
**解决方案**：
- 确认连接状态正常
- 检查端口号选择是否正确
- 查看日志中的错误信息
- 手动刷新端口状态显示

#### 4. 事件报告发送失败
**症状**：模拟事件后Host未收到报告
**解决方案**：
- 确认事件已通过S2F35正确链接到报告
- 检查事件是否通过S2F37启用
- 确认报告已通过S2F33正确定义
- 查看消息日志中的发送状态

### 日志分析

#### 关键日志信息
- **连接状态变化**：`Connection state changed to: Selected`
- **消息接收**：`收到SEMI消息: S1F13`
- **消息发送**：`发送SEMI消息: S1F14`
- **命令处理**：`收到主机命令: PORT_READY_TO_UNLOAD`
- **事件发送**：`发送事件报告: 事件ID 1`

#### 错误信息解读
- **连接超时**：网络连接问题或Host未响应
- **消息格式错误**：SEMI消息格式不符合标准
- **命令执行失败**：主机命令参数错误或执行异常
- **事件发送失败**：事件配置错误或连接问题

## 📞 技术支持

如遇到问题，请：
1. 查看消息日志中的详细错误信息
2. 检查配置文件`config/secs-gem-config.json`
3. 确认SEMI协议版本兼容性
4. 联系技术支持团队

---

**版本**：V1.0.0  
**更新日期**：2024年10月  
**适用范围**：SEMI E5标准兼容的Host系统
