{"format": 1, "restore": {"D:\\wrqData\\work\\stkc\\N2-STB\\SemiTester\\SemiTester.csproj": {}}, "projects": {"D:\\wrqData\\work\\stkc\\N2-STB\\Proj.API\\Proj.API.csproj": {"version": "1.2.0", "restore": {"projectUniqueName": "D:\\wrqData\\work\\stkc\\N2-STB\\Proj.API\\Proj.API.csproj", "projectName": "Proj.API", "projectPath": "D:\\wrqData\\work\\stkc\\N2-STB\\Proj.API\\Proj.API.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\wrqData\\work\\stkc\\N2-STB\\Proj.API\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\wrqData\\work\\stkc\\N2-STB\\Proj.Log\\Proj.Log.csproj": {"projectPath": "D:\\wrqData\\work\\stkc\\N2-STB\\Proj.Log\\Proj.Log.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Hosting.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Options": {"target": "Package", "version": "[8.0.0, )"}, "Secs4Net": {"target": "Package", "version": "[2.4.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "D:\\wrqData\\work\\stkc\\N2-STB\\Proj.Log\\Proj.Log.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\wrqData\\work\\stkc\\N2-STB\\Proj.Log\\Proj.Log.csproj", "projectName": "Proj.<PERSON><PERSON>", "projectPath": "D:\\wrqData\\work\\stkc\\N2-STB\\Proj.Log\\Proj.Log.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\wrqData\\work\\stkc\\N2-STB\\Proj.Log\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"log4net": {"target": "Package", "version": "[2.0.17, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "D:\\wrqData\\work\\stkc\\N2-STB\\SemiTester\\SemiTester.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\wrqData\\work\\stkc\\N2-STB\\SemiTester\\SemiTester.csproj", "projectName": "SemiTester", "projectPath": "D:\\wrqData\\work\\stkc\\N2-STB\\SemiTester\\SemiTester.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\wrqData\\work\\stkc\\N2-STB\\SemiTester\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"D:\\wrqData\\work\\stkc\\N2-STB\\Proj.API\\Proj.API.csproj": {"projectPath": "D:\\wrqData\\work\\stkc\\N2-STB\\Proj.API\\Proj.API.csproj"}, "D:\\wrqData\\work\\stkc\\N2-STB\\Proj.Log\\Proj.Log.csproj": {"projectPath": "D:\\wrqData\\work\\stkc\\N2-STB\\Proj.Log\\Proj.Log.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.Options": {"target": "Package", "version": "[8.0.2, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Secs4Net": {"target": "Package", "version": "[2.4.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}