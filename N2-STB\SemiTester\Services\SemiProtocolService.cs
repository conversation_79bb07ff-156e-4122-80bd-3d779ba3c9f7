using Microsoft.Extensions.Logging;
using Proj.API;
using Proj.API.Services;
using Secs4Net;
using System.Collections.Concurrent;

namespace SemiTester.Services;

/// <summary>
/// SEMI协议服务实现 - 增强版，支持完整的命令自动响应
/// </summary>
public class SemiProtocolService : ISemiProtocolService
{
    private readonly ILogger<SemiProtocolService> _logger;
    private readonly ISecsConnectionFactory _connectionFactory;
    private readonly IMessageLogService _messageLogService;
    private IGemEquipment? _gemEquipment;
    private bool _disposed = false;

    // 端口数据模拟存储
    private readonly ConcurrentDictionary<uint, Dictionary<uint, object>> _portDataStorage = new();

    public SemiProtocolService(
        ILogger<SemiProtocolService> logger,
        ISecsConnectionFactory connectionFactory,
        IMessageLogService messageLogService)
    {
        _logger = logger;
        _connectionFactory = connectionFactory;
        _messageLogService = messageLogService;
        ConnectionConfig = new SemiConnectionConfig();

        // 初始化端口数据
        InitializePortData();
    }

    /// <summary>
    /// 初始化端口数据
    /// </summary>
    private void InitializePortData()
    {
        for (uint i = 1; i <= 4; i++)
        {
            var portData = new Dictionary<uint, object>
            {
                [1] = "IDLE",           // 状态
                [2] = "",               // FOUP ID
                [3] = 25.0f,            // 温度
                [4] = 1013.25f,         // 压力
                [5] = 0.0f,             // 流量
                [6] = 45.0f             // 湿度
            };
            _portDataStorage[i] = portData;
        }
    }

    public bool IsConnected => _gemEquipment?.IsConnected ?? false;
    public int CommunicationState => _gemEquipment?.CommunicationState ?? 0;
    public int ControlState => _gemEquipment?.ControlState ?? 0;
    public string MDLN { get; set; } = "SEMI_TESTER";
    public string SoftRev { get; set; } = "V1.0.0";
    public SemiConnectionConfig ConnectionConfig { get; set; }

    public event EventHandler<ConnectionStateChangedEventArgs>? ConnectionStateChanged;
    public event EventHandler<MessageReceivedEventArgs>? MessageReceived;
    public event EventHandler<MessageSentEventArgs>? MessageSent;
    public event EventHandler<ErrorEventArgs>? ErrorOccurred;

    public async Task<bool> ConnectAsync()
    {
        try
        {
            _logger.LogInformation("开始连接到 {IpAddress}:{Port}", ConnectionConfig.IpAddress, ConnectionConfig.Port);

            // 创建基础的GEM设备实例
            _gemEquipment = new GemEquipmentImpl(
                new SecsGemConfigService(),
                _connectionFactory,
                new SecsLogger());

            _gemEquipment.MDLN = MDLN;
            _gemEquipment.SoftRev = SoftRev;

            // 订阅事件
            _gemEquipment.ConnectionChanged += OnConnectionChanged;

            // 启动连接
            await _gemEquipment.ConnectAsync();

            // 等待连接建立
            var timeout = TimeSpan.FromSeconds(30);
            var startTime = DateTime.Now;
            
            while (DateTime.Now - startTime < timeout)
            {
                if (IsConnected)
                {
                    _logger.LogInformation("连接成功建立");
                    return true;
                }
                await Task.Delay(100);
            }

            _logger.LogWarning("连接超时");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "连接失败");
            ErrorOccurred?.Invoke(this, new ErrorEventArgs($"连接失败: {ex.Message}", ex));
            return false;
        }
    }

    public async Task DisconnectAsync()
    {
        try
        {
            if (_gemEquipment != null)
            {
                _gemEquipment.ConnectionChanged -= OnConnectionChanged;
                _gemEquipment.Disconnect();
                _gemEquipment.Dispose();
                _gemEquipment = null;
            }
            
            _logger.LogInformation("连接已断开");
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "断开连接时发生错误");
            ErrorOccurred?.Invoke(this, new ErrorEventArgs($"断开连接失败: {ex.Message}", ex));
        }
    }

    public async Task<SecsMessage?> SendMessageAsync(byte stream, byte function, Item? data = null, int timeoutSeconds = 10)
    {
        if (_gemEquipment == null || !IsConnected)
        {
            throw new InvalidOperationException("设备未连接");
        }

        try
        {
            var message = new SecsMessage(stream, function) { SecsItem = data ?? Item.L() };
            
            _logger.LogDebug("发送消息 S{Stream}F{Function}", stream, function);
            
            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(timeoutSeconds));
            var response = await _gemEquipment.SendAsync(message, cts.Token);
            
            MessageSent?.Invoke(this, new MessageSentEventArgs(message, true));
            
            if (response != null)
            {
                MessageReceived?.Invoke(this, new MessageReceivedEventArgs(response, false));
            }
            
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送消息失败 S{Stream}F{Function}", stream, function);
            ErrorOccurred?.Invoke(this, new ErrorEventArgs($"发送消息失败: {ex.Message}", ex));
            return null;
        }
    }

    public async Task<bool> EstablishCommunicationAsync()
    {
        try
        {
            var response = await SendMessageAsync(1, 13);
            return response?.S == 1 && response?.F == 14;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "建立通信失败");
            return false;
        }
    }

    public async Task<bool> RequestOnlineAsync()
    {
        try
        {
            var response = await SendMessageAsync(1, 17);
            return response?.S == 1 && response?.F == 18;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "请求上线失败");
            return false;
        }
    }

    public async Task<bool> RequestOfflineAsync()
    {
        try
        {
            var response = await SendMessageAsync(1, 15);
            return response?.S == 1 && response?.F == 16;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "请求下线失败");
            return false;
        }
    }

    public async Task<bool> SwitchToLocalAsync()
    {
        try
        {
            if (_gemEquipment != null)
            {
                return await _gemEquipment.SwitchLocalAsync();
            }
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "切换到本地模式失败");
            return false;
        }
    }

    public async Task<bool> SwitchToRemoteAsync()
    {
        try
        {
            if (_gemEquipment != null)
            {
                return await _gemEquipment.SwitchRemoteAsync();
            }
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "切换到远程模式失败");
            return false;
        }
    }

    public async Task<(int communicationState, int controlState)?> GetEquipmentStatusAsync()
    {
        try
        {
            var response = await SendMessageAsync(1, 3);
            if (response?.S == 1 && response?.F == 4 && response.SecsItem?.Count >= 2)
            {
                var commState = Convert.ToInt32(response.SecsItem[0]);
                var ctrlState = Convert.ToInt32(response.SecsItem[1]);
                return (commState, ctrlState);
            }
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取设备状态失败");
            return null;
        }
    }

    public async Task<DateTime?> GetDateTimeAsync()
    {
        try
        {
            var response = await SendMessageAsync(2, 17);
            if (response?.S == 2 && response?.F == 18 && response.SecsItem != null)
            {
                var timeStr = response.SecsItem.GetString();
                if (DateTime.TryParseExact(timeStr, "yyyyMMddHHmmss", null, System.Globalization.DateTimeStyles.None, out var dateTime))
                {
                    return dateTime;
                }
            }
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取时间失败");
            return null;
        }
    }

    public async Task<bool> SetDateTimeAsync(DateTime dateTime)
    {
        try
        {
            var timeStr = dateTime.ToString("yyyyMMddHHmmss");
            var response = await SendMessageAsync(2, 31, Item.A(timeStr));
            return response?.S == 2 && response?.F == 32;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置时间失败");
            return false;
        }
    }

    public async Task<bool> SendAreYouThereAsync()
    {
        try
        {
            var response = await SendMessageAsync(1, 1);
            return response?.S == 1 && response?.F == 2;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Are You There测试失败");
            return false;
        }
    }

    private void OnConnectionChanged(object? sender, ConnectionState newState)
    {
        var oldState = ConnectionState.Retry; // 简化处理
        ConnectionStateChanged?.Invoke(this, new ConnectionStateChangedEventArgs(newState, oldState));
        _logger.LogInformation("连接状态变化: {NewState}", newState);
    }

    #region 增强功能方法

    /// <summary>
    /// 设置端口状态
    /// </summary>
    public void SetPortStatus(uint portNumber, string status, string foupId = "")
    {
        if (_portDataStorage.TryGetValue(portNumber, out var portData))
        {
            portData[1] = status;
            portData[2] = foupId;
            portData[7] = DateTime.Now.ToString("HH:mm:ss"); // 更新时间

            _logger.LogInformation($"设置Port{portNumber}状态: {status}, FOUP ID: {foupId}");
        }
    }

    /// <summary>
    /// 获取端口状态
    /// </summary>
    public Dictionary<uint, object> GetPortStatus(uint portNumber)
    {
        return _portDataStorage.TryGetValue(portNumber, out var data)
            ? new Dictionary<uint, object>(data)
            : new Dictionary<uint, object>();
    }

    /// <summary>
    /// 发送事件报告
    /// </summary>
    public async Task<bool> SendEventReportAsync(uint eventId, Dictionary<string, object> eventData)
    {
        if (_gemEquipment != null)
        {
            return await _gemEquipment.SendEventReportAsync(eventId, eventData);
        }
        return false;
    }

    /// <summary>
    /// 模拟FOUP放置事件
    /// </summary>
    public async Task<bool> SimulateFoupPlacedAsync(uint portNumber, string foupId)
    {
        if (_gemEquipment == null) return false;

        SetPortStatus(portNumber, "FOUP_PLACED", foupId);

        var eventData = new Dictionary<string, object>
        {
            ["PortNumber"] = portNumber,
            ["FoupId"] = foupId,
            ["PlacedTime"] = DateTime.Now.ToString("yyyyMMddHHmmss")
        };

        return await SendEventReportAsync(1, eventData); // 假设事件ID 1 为FOUP放置事件
    }

    /// <summary>
    /// 模拟FOUP移除事件
    /// </summary>
    public async Task<bool> SimulateFoupRemovedAsync(uint portNumber)
    {
        if (_gemEquipment == null) return false;

        SetPortStatus(portNumber, "FOUP_REMOVED", "");

        var eventData = new Dictionary<string, object>
        {
            ["PortNumber"] = portNumber,
            ["RemovedTime"] = DateTime.Now.ToString("yyyyMMddHHmmss")
        };

        return await SendEventReportAsync(2, eventData); // 假设事件ID 2 为FOUP移除事件
    }

    /// <summary>
    /// 模拟充气完成事件
    /// </summary>
    public async Task<bool> SimulatePurgeCompletedAsync(uint portNumber)
    {
        if (_gemEquipment == null) return false;

        SetPortStatus(portNumber, "PURGE_COMPLETED");

        var eventData = new Dictionary<string, object>
        {
            ["PortNumber"] = portNumber,
            ["CompletedTime"] = DateTime.Now.ToString("yyyyMMddHHmmss")
        };

        return await SendEventReportAsync(3, eventData); // 假设事件ID 3 为充气完成事件
    }

    #endregion

    public void Dispose()
    {
        if (!_disposed)
        {
            DisconnectAsync().Wait();
            _disposed = true;
        }
    }
}
