using System.Collections.Concurrent;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using Secs4Net;

namespace SemiTester.Services;

/// <summary>
/// 消息日志服务实现
/// </summary>
public class MessageLogService : IMessageLogService
{
    private readonly ILogger<MessageLogService> _logger;
    private readonly ConcurrentQueue<MessageLogEntry> _messageHistory = new();
    private readonly int _maxHistoryCount = 10000;

    public MessageLogService(ILogger<MessageLogService> logger)
    {
        _logger = logger;
    }

    public event EventHandler<MessageLogEventArgs>? MessageLogged;

    public void LogSentMessage(SecsMessage message, bool success, TimeSpan? responseTime = null)
    {
        var entry = new MessageLogEntry
        {
            Timestamp = DateTime.Now,
            Direction = MessageDirection.Sent,
            MessageType = $"S{message.S}F{message.F}",
            Content = FormatMessage(message),
            Success = success,
            ResponseTime = responseTime
        };

        AddLogEntry(entry);
        _logger.LogInformation("发送消息: {MessageType}, 成功: {Success}", entry.MessageType, success);
    }

    public void LogReceivedMessage(SecsMessage message, bool isPrimary = true)
    {
        var entry = new MessageLogEntry
        {
            Timestamp = DateTime.Now,
            Direction = MessageDirection.Received,
            MessageType = $"S{message.S}F{message.F}",
            Content = FormatMessage(message),
            Success = true
        };

        AddLogEntry(entry);
        _logger.LogInformation("接收消息: {MessageType}, 主消息: {IsPrimary}", entry.MessageType, isPrimary);
    }

    public void LogError(string message, Exception? exception = null)
    {
        var entry = new MessageLogEntry
        {
            Timestamp = DateTime.Now,
            Direction = MessageDirection.Error,
            MessageType = "ERROR",
            Content = message,
            Success = false,
            ErrorMessage = exception?.Message
        };

        AddLogEntry(entry);
        _logger.LogError(exception, "错误: {Message}", message);
    }

    public void LogInfo(string message)
    {
        var entry = new MessageLogEntry
        {
            Timestamp = DateTime.Now,
            Direction = MessageDirection.Info,
            MessageType = "INFO",
            Content = message,
            Success = true
        };

        AddLogEntry(entry);
        _logger.LogInformation("信息: {Message}", message);
    }

    public IEnumerable<MessageLogEntry> GetMessageHistory(int maxCount = 1000)
    {
        return _messageHistory.TakeLast(maxCount);
    }

    public void ClearHistory()
    {
        while (_messageHistory.TryDequeue(out _)) { }
        _logger.LogInformation("消息历史已清除");
    }

    public async Task<bool> ExportHistoryAsync(string filePath)
    {
        try
        {
            var history = GetMessageHistory().ToList();
            var json = JsonSerializer.Serialize(history, new JsonSerializerOptions 
            { 
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            });

            await File.WriteAllTextAsync(filePath, json, Encoding.UTF8);
            _logger.LogInformation("消息历史已导出到: {FilePath}", filePath);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导出消息历史失败: {FilePath}", filePath);
            return false;
        }
    }

    private void AddLogEntry(MessageLogEntry entry)
    {
        _messageHistory.Enqueue(entry);

        // 限制历史记录数量
        while (_messageHistory.Count > _maxHistoryCount)
        {
            _messageHistory.TryDequeue(out _);
        }

        MessageLogged?.Invoke(this, new MessageLogEventArgs(entry));
    }

    private static string FormatMessage(SecsMessage message)
    {
        var sb = new StringBuilder();
        sb.AppendLine($"S{message.S}F{message.F}");
        
        if (message.SecsItem != null)
        {
            sb.AppendLine("Data:");
            sb.AppendLine(FormatItem(message.SecsItem, 0));
        }
        else
        {
            sb.AppendLine("No Data");
        }

        return sb.ToString();
    }

    private static string FormatItem(Item item, int indent)
    {
        var indentStr = new string(' ', indent * 2);
        var sb = new StringBuilder();

        switch (item.Format)
        {
            case SecsFormat.List:
                sb.AppendLine($"{indentStr}List[{item.Count}]:");
                for (int i = 0; i < item.Count; i++)
                {
                    sb.AppendLine($"{indentStr}  [{i}]:");
                    sb.Append(FormatItem(item[i], indent + 2));
                }
                break;

            case SecsFormat.ASCII:
                sb.AppendLine($"{indentStr}ASCII: \"{item.GetString()}\"");
                break;

            case SecsFormat.U1:
            case SecsFormat.U2:
            case SecsFormat.U4:
            case SecsFormat.I1:
            case SecsFormat.I2:
            case SecsFormat.I4:
            case SecsFormat.F4:
            case SecsFormat.F8:
                if (item.Count == 1)
                {
                    sb.AppendLine($"{indentStr}{item.Format}: {item[0]}");
                }
                else
                {
                    sb.AppendLine($"{indentStr}{item.Format}[{item.Count}]: [{string.Join(", ", Enumerable.Range(0, Math.Min(item.Count, 10)).Select(i => item[i].ToString()))}]");
                    if (item.Count > 10)
                    {
                        sb.AppendLine($"{indentStr}  ... and {item.Count - 10} more items");
                    }
                }
                break;

            case SecsFormat.Boolean:
                sb.AppendLine($"{indentStr}Boolean: {item[0]}");
                break;

            default:
                sb.AppendLine($"{indentStr}{item.Format}: {item}");
                break;
        }

        return sb.ToString();
    }
}
