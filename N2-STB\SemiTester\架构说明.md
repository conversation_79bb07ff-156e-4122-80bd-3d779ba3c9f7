# SEMI Protocol Tester - 架构说明

## 🏗️ 系统架构概览

SEMI Protocol Tester 采用现代化的分层架构设计，基于依赖注入和服务导向的架构模式，确保代码的可维护性、可扩展性和可测试性。

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
│                     (表示层)                                │
├─────────────────────────────────────────────────────────────┤
│                    Service Layer                            │
│                     (服务层)                                │
├─────────────────────────────────────────────────────────────┤
│                    Infrastructure Layer                     │
│                     (基础设施层)                             │
└─────────────────────────────────────────────────────────────┘
```

## 📁 项目结构

```
SemiTester/
├── Program.cs                 # 程序入口点和依赖注入配置
├── Forms/                     # 用户界面层
│   └── MainForm.cs           # 主窗体实现
├── Services/                  # 服务层
│   ├── ISemiProtocolService.cs      # SEMI协议服务接口
│   ├── SemiProtocolService.cs       # SEMI协议服务实现
│   ├── IMessageLogService.cs        # 消息日志服务接口
│   ├── MessageLogService.cs         # 消息日志服务实现
│   ├── IConfigurationManager.cs     # 配置管理接口
│   └── ConfigurationManager.cs      # 配置管理实现
├── Config/                    # 配置文件
│   └── config.json           # 应用程序配置
└── README.md                 # 使用文档
```

## 🔧 核心组件详解

### 1. 表示层 (Presentation Layer)

#### MainForm.cs
- **职责**: 用户界面交互和显示
- **特性**:
  - 采用选项卡式界面设计
  - 响应式UI更新
  - 事件驱动的用户交互
  - 线程安全的UI操作

**主要功能模块**:
- 连接设置页面：配置和管理SEMI连接
- 消息测试页面：发送和测试SEMI消息  
- 消息日志页面：查看和管理通信日志

### 2. 服务层 (Service Layer)

#### ISemiProtocolService / SemiProtocolService
- **职责**: SEMI/GEM协议通信管理
- **核心功能**:
  - 连接生命周期管理
  - SECS消息发送和接收
  - 协议状态管理
  - 事件通知机制

**关键方法**:
```csharp
Task<bool> ConnectAsync()                    // 建立连接
Task<SecsMessage?> SendMessageAsync(...)    // 发送消息
Task<bool> EstablishCommunicationAsync()    // 建立通信
Task<bool> RequestOnlineAsync()             // 请求上线
```

#### IMessageLogService / MessageLogService
- **职责**: 消息日志记录和管理
- **核心功能**:
  - 消息日志记录
  - 日志格式化和分类
  - 日志导出功能
  - 历史记录管理

**关键方法**:
```csharp
void LogSentMessage(...)         // 记录发送消息
void LogReceivedMessage(...)     // 记录接收消息
Task<bool> ExportHistoryAsync()  // 导出日志历史
```

#### IConfigurationManager / ConfigurationManager
- **职责**: 应用程序配置管理
- **核心功能**:
  - 配置文件加载和保存
  - 默认配置管理
  - 配置验证和错误处理

### 3. 基础设施层 (Infrastructure Layer)

#### 依赖的外部组件
- **Proj.API**: 提供GemEquipmentImpl核心SEMI/GEM实现
- **Proj.Log**: 提供SecsLogger日志记录功能
- **Secs4Net**: SEMI/GEM协议底层通信库
- **Microsoft.Extensions**: 依赖注入和配置管理

## 🔄 数据流和交互模式

### 连接建立流程
```
用户操作 → MainForm → SemiProtocolService → GemEquipmentImpl → Secs4Net → 设备
    ↓
状态更新 ← MainForm ← SemiProtocolService ← GemEquipmentImpl ← Secs4Net ← 设备
```

### 消息发送流程
```
用户输入 → MainForm → SemiProtocolService → GemEquipmentImpl → SECS消息 → 设备
    ↓
日志记录 → MessageLogService → 日志存储
    ↓
UI更新 ← MainForm ← 事件通知
```

### 配置管理流程
```
程序启动 → ConfigurationManager → 加载配置文件 → 应用配置
    ↓
配置修改 → ConfigurationManager → 保存配置文件
```

## 🎯 设计模式和原则

### 1. 依赖注入 (Dependency Injection)
- 使用Microsoft.Extensions.DependencyInjection
- 服务生命周期管理（Singleton, Transient）
- 接口和实现分离

### 2. 事件驱动架构 (Event-Driven Architecture)
- 异步事件通知机制
- 松耦合的组件通信
- 响应式用户界面更新

### 3. 服务定位模式 (Service Locator Pattern)
- 集中化的服务管理
- 运行时服务解析
- 灵活的组件替换

### 4. 策略模式 (Strategy Pattern)
- 可配置的连接策略
- 可扩展的消息处理策略
- 灵活的日志记录策略

## 🔐 线程安全和并发处理

### 异步编程模型
- 全面采用async/await模式
- 非阻塞的UI操作
- CancellationToken支持

### 线程安全措施
- ConcurrentQueue用于消息队列
- Invoke/BeginInvoke用于UI线程调用
- 适当的锁机制保护共享资源

### 错误处理策略
- 分层异常处理
- 优雅的错误恢复
- 详细的错误日志记录

## 🚀 性能优化

### 内存管理
- 及时释放资源
- IDisposable模式实现
- 弱引用避免内存泄漏

### 网络优化
- 连接池管理
- 消息缓冲机制
- 超时控制

### UI性能
- 虚拟化列表控件
- 延迟加载机制
- 后台线程处理

## 🔧 扩展性设计

### 插件架构支持
- 基于接口的扩展点
- 动态加载机制
- 配置驱动的功能启用

### 协议扩展
- 可插拔的消息处理器
- 自定义消息格式支持
- 协议版本兼容性

### UI扩展
- 可配置的界面布局
- 主题和样式支持
- 多语言国际化准备

## 📊 监控和诊断

### 日志系统
- 分级日志记录
- 结构化日志格式
- 日志轮转和归档

### 性能监控
- 连接状态监控
- 消息处理性能统计
- 资源使用情况跟踪

### 诊断工具
- 连接诊断功能
- 消息格式验证
- 网络连通性测试

## 🔄 版本控制和部署

### 版本管理
- 语义化版本控制
- 向后兼容性保证
- 配置文件版本迁移

### 部署策略
- 单文件部署支持
- 配置文件外部化
- 自动更新机制准备

---

这个架构设计确保了SEMI Protocol Tester具有良好的可维护性、可扩展性和性能表现，同时保持了代码的清晰性和可测试性。
