using System.Text.Json;
using Microsoft.Extensions.Logging;

namespace SemiTester.Services;

/// <summary>
/// 配置管理器实现
/// </summary>
public class ConfigurationManager : IConfigurationManager
{
    private readonly ILogger<ConfigurationManager> _logger;
    private readonly string _configFilePath;

    public ConfigurationManager(ILogger<ConfigurationManager> logger)
    {
        _logger = logger;
        _configFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config", "config.json");
    }

    public async Task<SemiTesterConfig> LoadConfigAsync()
    {
        try
        {
            if (!File.Exists(_configFilePath))
            {
                _logger.LogInformation("配置文件不存在，使用默认配置: {ConfigPath}", _configFilePath);
                var defaultConfig = GetDefaultConfig();
                await SaveConfigAsync(defaultConfig);
                return defaultConfig;
            }

            var json = await File.ReadAllTextAsync(_configFilePath);
            var config = JsonSerializer.Deserialize<SemiTesterConfig>(json);
            
            if (config == null)
            {
                _logger.LogWarning("配置文件格式错误，使用默认配置");
                return GetDefaultConfig();
            }

            _logger.LogInformation("配置加载成功: {ConfigPath}", _configFilePath);
            return config;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载配置失败，使用默认配置: {ConfigPath}", _configFilePath);
            return GetDefaultConfig();
        }
    }

    public async Task<bool> SaveConfigAsync(SemiTesterConfig config)
    {
        try
        {
            var directory = Path.GetDirectoryName(_configFilePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            var options = new JsonSerializerOptions
            {
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            };

            var json = JsonSerializer.Serialize(config, options);
            await File.WriteAllTextAsync(_configFilePath, json);

            _logger.LogInformation("配置保存成功: {ConfigPath}", _configFilePath);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存配置失败: {ConfigPath}", _configFilePath);
            return false;
        }
    }

    public SemiTesterConfig GetDefaultConfig()
    {
        return new SemiTesterConfig
        {
            Connection = new SemiConnectionConfig
            {
                IpAddress = "127.0.0.1",
                Port = 5000,
                DeviceId = 1,
                IsActive = true,
                T3Timeout = 45000,
                T5Timeout = 10000,
                T6Timeout = 5000,
                T7Timeout = 10000,
                T8Timeout = 5000
            },
            Device = new DeviceInfo
            {
                MDLN = "SEMI_TESTER",
                SoftRev = "V1.0.0",
                Description = "SEMI Protocol Tester"
            },
            UI = new UIConfig
            {
                AutoScroll = true,
                MaxLogEntries = 1000,
                ShowTimestamp = true,
                ShowMessageDetails = true,
                Theme = "Default"
            },
            Logging = new LogConfig
            {
                EnableFileLogging = true,
                LogDirectory = "Logs",
                MaxLogFiles = 10,
                MaxLogFileSize = 10 * 1024 * 1024
            }
        };
    }
}
