using Microsoft.Extensions.Logging;
using SemiTester.Services;
using Secs4Net;

namespace SemiTester.Forms;

/// <summary>
/// 主窗体
/// </summary>
public partial class MainForm : Form
{
    private readonly ISemiProtocolService _semiService;
    private readonly IMessageLogService _logService;
    private readonly IConfigurationManager _configManager;
    private readonly ILogger<MainForm> _logger;
    
    private SemiTesterConfig _config;
    private bool _isConnected = false;

    // UI控件
    private TabControl _mainTabControl = null!;
    private StatusStrip _statusStrip = null!;
    private ToolStripStatusLabel _connectionStatusLabel = null!;
    private ToolStripStatusLabel _messageCountLabel = null!;
    
    // 连接页面控件
    private TextBox _ipAddressTextBox = null!;
    private NumericUpDown _portNumericUpDown = null!;
    private NumericUpDown _deviceIdNumericUpDown = null!;
    private CheckBox _isActiveCheckBox = null!;
    private Button _connectButton = null!;
    private Button _disconnectButton = null!;
    private Label _connectionStateLabel = null!;
    
    // 消息页面控件
    private ComboBox _streamComboBox = null!;
    private ComboBox _functionComboBox = null!;
    private TextBox _messageDataTextBox = null!;
    private Button _sendMessageButton = null!;
    private Button _establishCommButton = null!;
    private Button _requestOnlineButton = null!;
    private Button _requestOfflineButton = null!;
    private Button _areYouThereButton = null!;
    
    // 日志页面控件
    private ListView _logListView = null!;
    private Button _clearLogButton = null!;
    private Button _exportLogButton = null!;
    private CheckBox _autoScrollCheckBox = null!;

    // 端口控制页面控件
    private ComboBox _portNumberComboBox = null!;
    private ComboBox _portStatusComboBox = null!;
    private TextBox _foupIdTextBox = null!;
    private Button _setPortStatusButton = null!;
    private Button _getPortStatusButton = null!;
    private Button _simulateFoupPlacedButton = null!;
    private Button _simulateFoupRemovedButton = null!;
    private Button _simulatePurgeCompletedButton = null!;
    private ListView _portStatusListView = null!;

    public MainForm(
        ISemiProtocolService semiService,
        IMessageLogService logService,
        IConfigurationManager configManager,
        ILogger<MainForm> logger)
    {
        _semiService = semiService;
        _logService = logService;
        _configManager = configManager;
        _logger = logger;
        
        _config = _configManager.GetDefaultConfig();
        
        InitializeComponent();
        InitializeEvents();
        LoadConfigurationAsync();
    }

    private void InitializeComponent()
    {
        Text = "SEMI Protocol Tester";
        Size = new Size(1200, 800);
        StartPosition = FormStartPosition.CenterScreen;

        // 创建主要布局
        _mainTabControl = new TabControl
        {
            Dock = DockStyle.Fill
        };

        // 创建状态栏
        _statusStrip = new StatusStrip();
        _connectionStatusLabel = new ToolStripStatusLabel("未连接") { Spring = true, TextAlign = ContentAlignment.MiddleLeft };
        _messageCountLabel = new ToolStripStatusLabel("消息: 0");
        _statusStrip.Items.AddRange(new ToolStripItem[] { _connectionStatusLabel, _messageCountLabel });

        // 创建选项卡页面
        CreateConnectionTab();
        CreateMessageTab();
        CreatePortControlTab();
        CreateLogTab();

        // 添加控件到窗体
        Controls.Add(_mainTabControl);
        Controls.Add(_statusStrip);
    }

    private void CreateConnectionTab()
    {
        var tabPage = new TabPage("连接设置");
        var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

        // 连接配置组
        var configGroup = new GroupBox { Text = "连接配置", Size = new Size(400, 200), Location = new Point(10, 10) };
        var configLayout = new TableLayoutPanel { Dock = DockStyle.Fill, ColumnCount = 2, RowCount = 4 };

        configLayout.Controls.Add(new Label { Text = "IP地址:", TextAlign = ContentAlignment.MiddleRight }, 0, 0);
        _ipAddressTextBox = new TextBox { Width = 200 };
        configLayout.Controls.Add(_ipAddressTextBox, 1, 0);

        configLayout.Controls.Add(new Label { Text = "端口:", TextAlign = ContentAlignment.MiddleRight }, 0, 1);
        _portNumericUpDown = new NumericUpDown { Minimum = 1, Maximum = 65535, Width = 200 };
        configLayout.Controls.Add(_portNumericUpDown, 1, 1);

        configLayout.Controls.Add(new Label { Text = "设备ID:", TextAlign = ContentAlignment.MiddleRight }, 0, 2);
        _deviceIdNumericUpDown = new NumericUpDown { Minimum = 1, Maximum = 32767, Width = 200 };
        configLayout.Controls.Add(_deviceIdNumericUpDown, 1, 2);

        _isActiveCheckBox = new CheckBox { Text = "主动连接模式" };
        configLayout.Controls.Add(_isActiveCheckBox, 1, 3);

        configGroup.Controls.Add(configLayout);
        panel.Controls.Add(configGroup);

        // 连接控制组
        var controlGroup = new GroupBox { Text = "连接控制", Size = new Size(400, 100), Location = new Point(10, 220) };
        var controlLayout = new FlowLayoutPanel { Dock = DockStyle.Fill, FlowDirection = FlowDirection.LeftToRight };

        _connectButton = new Button { Text = "连接", Size = new Size(80, 30) };
        _disconnectButton = new Button { Text = "断开", Size = new Size(80, 30), Enabled = false };
        controlLayout.Controls.AddRange(new Control[] { _connectButton, _disconnectButton });

        controlGroup.Controls.Add(controlLayout);
        panel.Controls.Add(controlGroup);

        // 状态显示组
        var statusGroup = new GroupBox { Text = "连接状态", Size = new Size(400, 80), Location = new Point(10, 330) };
        _connectionStateLabel = new Label { Text = "未连接", Dock = DockStyle.Fill, TextAlign = ContentAlignment.MiddleCenter, Font = new Font("Microsoft YaHei", 12, FontStyle.Bold) };
        statusGroup.Controls.Add(_connectionStateLabel);
        panel.Controls.Add(statusGroup);

        tabPage.Controls.Add(panel);
        _mainTabControl.TabPages.Add(tabPage);
    }

    private void CreateMessageTab()
    {
        var tabPage = new TabPage("消息测试");
        var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

        // 消息发送组
        var sendGroup = new GroupBox { Text = "发送消息", Size = new Size(500, 150), Location = new Point(10, 10) };
        var sendLayout = new TableLayoutPanel { Dock = DockStyle.Fill, ColumnCount = 4, RowCount = 3 };

        sendLayout.Controls.Add(new Label { Text = "Stream:", TextAlign = ContentAlignment.MiddleRight }, 0, 0);
        _streamComboBox = new ComboBox { Width = 80, DropDownStyle = ComboBoxStyle.DropDownList };
        for (int i = 1; i <= 10; i++) _streamComboBox.Items.Add(i);
        _streamComboBox.SelectedIndex = 0;
        sendLayout.Controls.Add(_streamComboBox, 1, 0);

        sendLayout.Controls.Add(new Label { Text = "Function:", TextAlign = ContentAlignment.MiddleRight }, 2, 0);
        _functionComboBox = new ComboBox { Width = 80, DropDownStyle = ComboBoxStyle.DropDownList };
        for (int i = 1; i <= 50; i++) _functionComboBox.Items.Add(i);
        _functionComboBox.SelectedIndex = 0;
        sendLayout.Controls.Add(_functionComboBox, 3, 0);

        sendLayout.Controls.Add(new Label { Text = "数据:", TextAlign = ContentAlignment.MiddleRight }, 0, 1);
        _messageDataTextBox = new TextBox { Width = 300, Height = 60, Multiline = true, ScrollBars = ScrollBars.Vertical };
        sendLayout.Controls.Add(_messageDataTextBox, 1, 1);
        sendLayout.SetColumnSpan(_messageDataTextBox, 3);

        _sendMessageButton = new Button { Text = "发送消息", Size = new Size(100, 30) };
        sendLayout.Controls.Add(_sendMessageButton, 1, 2);

        sendGroup.Controls.Add(sendLayout);
        panel.Controls.Add(sendGroup);

        // 快捷操作组
        var quickGroup = new GroupBox { Text = "快捷操作", Size = new Size(500, 100), Location = new Point(10, 170) };
        var quickLayout = new FlowLayoutPanel { Dock = DockStyle.Fill, FlowDirection = FlowDirection.LeftToRight };

        _establishCommButton = new Button { Text = "建立通信", Size = new Size(100, 30) };
        _requestOnlineButton = new Button { Text = "请求上线", Size = new Size(100, 30) };
        _requestOfflineButton = new Button { Text = "请求下线", Size = new Size(100, 30) };
        _areYouThereButton = new Button { Text = "Are You There", Size = new Size(120, 30) };

        quickLayout.Controls.AddRange(new Control[] { 
            _establishCommButton, _requestOnlineButton, _requestOfflineButton, _areYouThereButton 
        });

        quickGroup.Controls.Add(quickLayout);
        panel.Controls.Add(quickGroup);

        tabPage.Controls.Add(panel);
        _mainTabControl.TabPages.Add(tabPage);
    }

    private void CreatePortControlTab()
    {
        var tabPage = new TabPage("端口控制");
        var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

        // 端口操作组
        var portGroup = new GroupBox { Text = "端口操作", Size = new Size(400, 250), Location = new Point(10, 10) };
        var portLayout = new TableLayoutPanel { Dock = DockStyle.Fill, ColumnCount = 2, RowCount = 6 };

        // 端口号选择
        portLayout.Controls.Add(new Label { Text = "端口号:", TextAlign = ContentAlignment.MiddleRight }, 0, 0);
        _portNumberComboBox = new ComboBox { Width = 200, DropDownStyle = ComboBoxStyle.DropDownList };
        _portNumberComboBox.Items.AddRange(new object[] { "1", "2", "3", "4" });
        _portNumberComboBox.SelectedIndex = 0;
        portLayout.Controls.Add(_portNumberComboBox, 1, 0);

        // 端口状态选择
        portLayout.Controls.Add(new Label { Text = "端口状态:", TextAlign = ContentAlignment.MiddleRight }, 0, 1);
        _portStatusComboBox = new ComboBox { Width = 200, DropDownStyle = ComboBoxStyle.DropDownList };
        _portStatusComboBox.Items.AddRange(new object[] { "IDLE", "FOUP_PLACED", "PURGING", "PURGE_COMPLETED", "READY_TO_UNLOAD", "READY_TO_LOAD" });
        _portStatusComboBox.SelectedIndex = 0;
        portLayout.Controls.Add(_portStatusComboBox, 1, 1);

        // FOUP ID输入
        portLayout.Controls.Add(new Label { Text = "FOUP ID:", TextAlign = ContentAlignment.MiddleRight }, 0, 2);
        _foupIdTextBox = new TextBox { Width = 200, PlaceholderText = "输入FOUP ID" };
        portLayout.Controls.Add(_foupIdTextBox, 1, 2);

        // 操作按钮
        var buttonPanel = new FlowLayoutPanel { FlowDirection = FlowDirection.LeftToRight, AutoSize = true };
        _setPortStatusButton = new Button { Text = "设置端口状态", Size = new Size(100, 30) };
        _getPortStatusButton = new Button { Text = "获取端口状态", Size = new Size(100, 30) };
        buttonPanel.Controls.AddRange(new Control[] { _setPortStatusButton, _getPortStatusButton });
        portLayout.Controls.Add(buttonPanel, 1, 3);

        portGroup.Controls.Add(portLayout);

        // 事件模拟组
        var eventGroup = new GroupBox { Text = "事件模拟", Size = new Size(400, 150), Location = new Point(10, 270) };
        var eventLayout = new FlowLayoutPanel { Dock = DockStyle.Fill, FlowDirection = FlowDirection.TopDown };

        _simulateFoupPlacedButton = new Button { Text = "模拟FOUP放置", Size = new Size(150, 30) };
        _simulateFoupRemovedButton = new Button { Text = "模拟FOUP移除", Size = new Size(150, 30) };
        _simulatePurgeCompletedButton = new Button { Text = "模拟充气完成", Size = new Size(150, 30) };

        eventLayout.Controls.AddRange(new Control[] {
            _simulateFoupPlacedButton,
            _simulateFoupRemovedButton,
            _simulatePurgeCompletedButton
        });
        eventGroup.Controls.Add(eventLayout);

        // 端口状态显示
        var statusGroup = new GroupBox { Text = "端口状态", Size = new Size(750, 400), Location = new Point(420, 10) };
        _portStatusListView = new ListView
        {
            Dock = DockStyle.Fill,
            View = View.Details,
            FullRowSelect = true,
            GridLines = true
        };
        _portStatusListView.Columns.AddRange(new ColumnHeader[]
        {
            new() { Text = "端口", Width = 60 },
            new() { Text = "状态", Width = 120 },
            new() { Text = "FOUP ID", Width = 100 },
            new() { Text = "温度(°C)", Width = 80 },
            new() { Text = "压力(Pa)", Width = 80 },
            new() { Text = "流量(L/min)", Width = 80 },
            new() { Text = "湿度(%)", Width = 80 },
            new() { Text = "更新时间", Width = 140 }
        });

        // 初始化端口状态显示
        for (int i = 1; i <= 4; i++)
        {
            var item = new ListViewItem(i.ToString());
            item.SubItems.AddRange(new string[] { "IDLE", "", "25.0", "1013.25", "0.0", "45.0", DateTime.Now.ToString("HH:mm:ss") });
            _portStatusListView.Items.Add(item);
        }

        statusGroup.Controls.Add(_portStatusListView);

        panel.Controls.AddRange(new Control[] { portGroup, eventGroup, statusGroup });
        tabPage.Controls.Add(panel);
        _mainTabControl.TabPages.Add(tabPage);
    }

    private void CreateLogTab()
    {
        var tabPage = new TabPage("消息日志");
        var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

        // 工具栏
        var toolPanel = new Panel { Height = 40, Dock = DockStyle.Top };
        var toolLayout = new FlowLayoutPanel { Dock = DockStyle.Fill, FlowDirection = FlowDirection.LeftToRight };

        _clearLogButton = new Button { Text = "清除日志", Size = new Size(80, 30) };
        _exportLogButton = new Button { Text = "导出日志", Size = new Size(80, 30) };
        _autoScrollCheckBox = new CheckBox { Text = "自动滚动", Checked = true };

        toolLayout.Controls.AddRange(new Control[] { _clearLogButton, _exportLogButton, _autoScrollCheckBox });
        toolPanel.Controls.Add(toolLayout);

        // 日志列表
        _logListView = new ListView
        {
            Dock = DockStyle.Fill,
            View = View.Details,
            FullRowSelect = true,
            GridLines = true,
            MultiSelect = false
        };

        _logListView.Columns.AddRange(new ColumnHeader[]
        {
            new() { Text = "时间", Width = 150 },
            new() { Text = "方向", Width = 80 },
            new() { Text = "消息", Width = 100 },
            new() { Text = "状态", Width = 80 },
            new() { Text = "内容", Width = 400 }
        });

        panel.Controls.Add(_logListView);
        panel.Controls.Add(toolPanel);

        tabPage.Controls.Add(panel);
        _mainTabControl.TabPages.Add(tabPage);
    }

    private void InitializeEvents()
    {
        // 连接事件
        _connectButton.Click += OnConnectClick;
        _disconnectButton.Click += OnDisconnectClick;

        // 消息事件
        _sendMessageButton.Click += OnSendMessageClick;
        _establishCommButton.Click += OnEstablishCommClick;
        _requestOnlineButton.Click += OnRequestOnlineClick;
        _requestOfflineButton.Click += OnRequestOfflineClick;
        _areYouThereButton.Click += OnAreYouThereClick;

        // 日志事件
        _clearLogButton.Click += OnClearLogClick;
        _exportLogButton.Click += OnExportLogClick;

        // 端口控制事件
        _setPortStatusButton.Click += OnSetPortStatusClick;
        _getPortStatusButton.Click += OnGetPortStatusClick;
        _simulateFoupPlacedButton.Click += OnSimulateFoupPlacedClick;
        _simulateFoupRemovedButton.Click += OnSimulateFoupRemovedClick;
        _simulatePurgeCompletedButton.Click += OnSimulatePurgeCompletedClick;

        // 服务事件
        _semiService.ConnectionStateChanged += OnConnectionStateChanged;
        _semiService.MessageReceived += OnMessageReceived;
        _semiService.MessageSent += OnMessageSent;
        _semiService.ErrorOccurred += OnErrorOccurred;

        _logService.MessageLogged += OnMessageLogged;

        // 窗体事件
        FormClosing += OnFormClosing;
    }

    private async void LoadConfigurationAsync()
    {
        try
        {
            _config = await _configManager.LoadConfigAsync();
            
            // 应用配置到UI
            _ipAddressTextBox.Text = _config.Connection.IpAddress;
            _portNumericUpDown.Value = _config.Connection.Port;
            _deviceIdNumericUpDown.Value = _config.Connection.DeviceId;
            _isActiveCheckBox.Checked = _config.Connection.IsActive;
            _autoScrollCheckBox.Checked = _config.UI.AutoScroll;

            // 应用配置到服务
            _semiService.ConnectionConfig = _config.Connection;
            _semiService.MDLN = _config.Device.MDLN;
            _semiService.SoftRev = _config.Device.SoftRev;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载配置失败");
            MessageBox.Show($"加载配置失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    #region 事件处理

    private async void OnConnectClick(object? sender, EventArgs e)
    {
        try
        {
            _connectButton.Enabled = false;
            _connectionStateLabel.Text = "连接中...";
            _connectionStateLabel.ForeColor = Color.Orange;

            // 更新配置
            _config.Connection.IpAddress = _ipAddressTextBox.Text;
            _config.Connection.Port = (int)_portNumericUpDown.Value;
            _config.Connection.DeviceId = (ushort)_deviceIdNumericUpDown.Value;
            _config.Connection.IsActive = _isActiveCheckBox.Checked;

            _semiService.ConnectionConfig = _config.Connection;

            var success = await _semiService.ConnectAsync();
            if (success)
            {
                _isConnected = true;
                _connectButton.Enabled = false;
                _disconnectButton.Enabled = true;
                _connectionStateLabel.Text = "已连接";
                _connectionStateLabel.ForeColor = Color.Green;
                _connectionStatusLabel.Text = "已连接";

                _logService.LogInfo($"成功连接到 {_config.Connection.IpAddress}:{_config.Connection.Port}");
            }
            else
            {
                _connectButton.Enabled = true;
                _connectionStateLabel.Text = "连接失败";
                _connectionStateLabel.ForeColor = Color.Red;
                _logService.LogError("连接失败");
            }
        }
        catch (Exception ex)
        {
            _connectButton.Enabled = true;
            _connectionStateLabel.Text = "连接错误";
            _connectionStateLabel.ForeColor = Color.Red;
            _logService.LogError($"连接异常: {ex.Message}", ex);
        }
    }

    private async void OnDisconnectClick(object? sender, EventArgs e)
    {
        try
        {
            await _semiService.DisconnectAsync();
            _isConnected = false;
            _connectButton.Enabled = true;
            _disconnectButton.Enabled = false;
            _connectionStateLabel.Text = "未连接";
            _connectionStateLabel.ForeColor = Color.Gray;
            _connectionStatusLabel.Text = "未连接";

            _logService.LogInfo("连接已断开");
        }
        catch (Exception ex)
        {
            _logService.LogError($"断开连接异常: {ex.Message}", ex);
        }
    }

    private async void OnSendMessageClick(object? sender, EventArgs e)
    {
        if (!_isConnected)
        {
            MessageBox.Show("请先连接设备", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        try
        {
            var stream = (byte)((int)_streamComboBox.SelectedItem!);
            var function = (byte)((int)_functionComboBox.SelectedItem!);

            Item? data = null;
            if (!string.IsNullOrWhiteSpace(_messageDataTextBox.Text))
            {
                // 简单处理：如果是纯文本，作为ASCII发送
                data = Item.A(_messageDataTextBox.Text);
            }

            var response = await _semiService.SendMessageAsync(stream, function, data);
            if (response != null)
            {
                _logService.LogInfo($"消息发送成功，收到回复: S{response.S}F{response.F}");
            }
            else
            {
                _logService.LogError("消息发送失败或无回复");
            }
        }
        catch (Exception ex)
        {
            _logService.LogError($"发送消息异常: {ex.Message}", ex);
        }
    }

    private async void OnEstablishCommClick(object? sender, EventArgs e)
    {
        if (!_isConnected)
        {
            MessageBox.Show("请先连接设备", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        try
        {
            var success = await _semiService.EstablishCommunicationAsync();
            if (success)
            {
                _logService.LogInfo("通信建立成功");
            }
            else
            {
                _logService.LogError("通信建立失败");
            }
        }
        catch (Exception ex)
        {
            _logService.LogError($"建立通信异常: {ex.Message}", ex);
        }
    }

    private async void OnRequestOnlineClick(object? sender, EventArgs e)
    {
        if (!_isConnected)
        {
            MessageBox.Show("请先连接设备", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        try
        {
            var success = await _semiService.RequestOnlineAsync();
            if (success)
            {
                _logService.LogInfo("请求上线成功");
            }
            else
            {
                _logService.LogError("请求上线失败");
            }
        }
        catch (Exception ex)
        {
            _logService.LogError($"请求上线异常: {ex.Message}", ex);
        }
    }

    private async void OnRequestOfflineClick(object? sender, EventArgs e)
    {
        if (!_isConnected)
        {
            MessageBox.Show("请先连接设备", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        try
        {
            var success = await _semiService.RequestOfflineAsync();
            if (success)
            {
                _logService.LogInfo("请求下线成功");
            }
            else
            {
                _logService.LogError("请求下线失败");
            }
        }
        catch (Exception ex)
        {
            _logService.LogError($"请求下线异常: {ex.Message}", ex);
        }
    }

    private async void OnAreYouThereClick(object? sender, EventArgs e)
    {
        if (!_isConnected)
        {
            MessageBox.Show("请先连接设备", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        try
        {
            var success = await _semiService.SendAreYouThereAsync();
            if (success)
            {
                _logService.LogInfo("Are You There 测试成功");
            }
            else
            {
                _logService.LogError("Are You There 测试失败");
            }
        }
        catch (Exception ex)
        {
            _logService.LogError($"Are You There 测试异常: {ex.Message}", ex);
        }
    }

    private void OnClearLogClick(object? sender, EventArgs e)
    {
        _logListView.Items.Clear();
        _logService.ClearHistory();
        _messageCountLabel.Text = "消息: 0";
    }

    private async void OnExportLogClick(object? sender, EventArgs e)
    {
        try
        {
            using var dialog = new SaveFileDialog
            {
                Filter = "JSON文件|*.json|所有文件|*.*",
                DefaultExt = "json",
                FileName = $"SemiLog_{DateTime.Now:yyyyMMdd_HHmmss}.json"
            };

            if (dialog.ShowDialog() == DialogResult.OK)
            {
                var success = await _logService.ExportHistoryAsync(dialog.FileName);
                if (success)
                {
                    MessageBox.Show("日志导出成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("日志导出失败", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"导出日志异常: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    #endregion

    #region 服务事件处理

    private void OnConnectionStateChanged(object? sender, ConnectionStateChangedEventArgs e)
    {
        if (InvokeRequired)
        {
            Invoke(new Action(() => OnConnectionStateChanged(sender, e)));
            return;
        }

        _connectionStatusLabel.Text = $"连接状态: {e.NewState}";
        _logService.LogInfo($"连接状态变化: {e.OldState} -> {e.NewState}");
    }

    private void OnMessageReceived(object? sender, MessageReceivedEventArgs e)
    {
        if (InvokeRequired)
        {
            Invoke(new Action(() => OnMessageReceived(sender, e)));
            return;
        }

        _logService.LogReceivedMessage(e.Message, e.IsPrimary);
    }

    private void OnMessageSent(object? sender, MessageSentEventArgs e)
    {
        if (InvokeRequired)
        {
            Invoke(new Action(() => OnMessageSent(sender, e)));
            return;
        }

        _logService.LogSentMessage(e.Message, e.Success);
    }

    private void OnErrorOccurred(object? sender, Services.ErrorEventArgs e)
    {
        if (InvokeRequired)
        {
            Invoke(new Action(() => OnErrorOccurred(sender, e)));
            return;
        }

        _logService.LogError(e.Message, e.Exception);
    }

    private void OnMessageLogged(object? sender, MessageLogEventArgs e)
    {
        if (InvokeRequired)
        {
            Invoke(new Action(() => OnMessageLogged(sender, e)));
            return;
        }

        var item = new ListViewItem(e.LogEntry.Timestamp.ToString("HH:mm:ss.fff"));
        item.SubItems.Add(GetDirectionText(e.LogEntry.Direction));
        item.SubItems.Add(e.LogEntry.MessageType);
        item.SubItems.Add(e.LogEntry.Success ? "成功" : "失败");
        item.SubItems.Add(e.LogEntry.Content.Replace("\r\n", " ").Replace("\n", " "));

        // 设置颜色
        switch (e.LogEntry.Direction)
        {
            case MessageDirection.Sent:
                item.ForeColor = Color.Blue;
                break;
            case MessageDirection.Received:
                item.ForeColor = Color.Green;
                break;
            case MessageDirection.Error:
                item.ForeColor = Color.Red;
                break;
            case MessageDirection.Info:
                item.ForeColor = Color.Black;
                break;
        }

        _logListView.Items.Add(item);

        // 限制日志条目数量
        while (_logListView.Items.Count > _config.UI.MaxLogEntries)
        {
            _logListView.Items.RemoveAt(0);
        }

        // 自动滚动
        if (_autoScrollCheckBox.Checked && _logListView.Items.Count > 0)
        {
            _logListView.Items[_logListView.Items.Count - 1].EnsureVisible();
        }

        // 更新消息计数
        _messageCountLabel.Text = $"消息: {_logListView.Items.Count}";
    }

    private static string GetDirectionText(MessageDirection direction)
    {
        return direction switch
        {
            MessageDirection.Sent => "发送",
            MessageDirection.Received => "接收",
            MessageDirection.Error => "错误",
            MessageDirection.Info => "信息",
            _ => "未知"
        };
    }

    #endregion

    #region 端口控制事件

    private async void OnSetPortStatusClick(object? sender, EventArgs e)
    {
        try
        {
            if (!_isConnected)
            {
                MessageBox.Show("请先连接到设备", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            uint portNumber = (uint)(_portNumberComboBox.SelectedIndex + 1);
            string status = _portStatusComboBox.SelectedItem?.ToString() ?? "IDLE";
            string foupId = _foupIdTextBox.Text.Trim();

            _semiService.SetPortStatus(portNumber, status, foupId);

            // 更新显示
            UpdatePortStatusDisplay(portNumber, status, foupId);

            _logger.LogInformation($"设置Port{portNumber}状态: {status}, FOUP ID: {foupId}");
            MessageBox.Show($"Port{portNumber}状态已设置为: {status}", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置端口状态失败");
            MessageBox.Show($"设置端口状态失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private async void OnGetPortStatusClick(object? sender, EventArgs e)
    {
        try
        {
            if (!_isConnected)
            {
                MessageBox.Show("请先连接到设备", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            uint portNumber = (uint)(_portNumberComboBox.SelectedIndex + 1);
            var portData = _semiService.GetPortStatus(portNumber);

            var statusText = $"Port{portNumber} 状态信息:\n";
            foreach (var kvp in portData)
            {
                statusText += $"  {GetVariableName(kvp.Key)}: {kvp.Value}\n";
            }

            MessageBox.Show(statusText, "端口状态", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取端口状态失败");
            MessageBox.Show($"获取端口状态失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private async void OnSimulateFoupPlacedClick(object? sender, EventArgs e)
    {
        try
        {
            if (!_isConnected)
            {
                MessageBox.Show("请先连接到设备", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            uint portNumber = (uint)(_portNumberComboBox.SelectedIndex + 1);
            string foupId = _foupIdTextBox.Text.Trim();

            if (string.IsNullOrEmpty(foupId))
            {
                foupId = $"FOUP_{portNumber}_{DateTime.Now:HHmmss}";
                _foupIdTextBox.Text = foupId;
            }

            bool success = await _semiService.SimulateFoupPlacedAsync(portNumber, foupId);

            if (success)
            {
                UpdatePortStatusDisplay(portNumber, "FOUP_PLACED", foupId);
                _logger.LogInformation($"模拟Port{portNumber} FOUP放置事件成功, FOUP ID: {foupId}");
                MessageBox.Show($"Port{portNumber} FOUP放置事件已发送", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                MessageBox.Show("发送FOUP放置事件失败", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "模拟FOUP放置事件失败");
            MessageBox.Show($"模拟FOUP放置事件失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private async void OnSimulateFoupRemovedClick(object? sender, EventArgs e)
    {
        try
        {
            if (!_isConnected)
            {
                MessageBox.Show("请先连接到设备", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            uint portNumber = (uint)(_portNumberComboBox.SelectedIndex + 1);

            bool success = await _semiService.SimulateFoupRemovedAsync(portNumber);

            if (success)
            {
                UpdatePortStatusDisplay(portNumber, "FOUP_REMOVED", "");
                _logger.LogInformation($"模拟Port{portNumber} FOUP移除事件成功");
                MessageBox.Show($"Port{portNumber} FOUP移除事件已发送", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                MessageBox.Show("发送FOUP移除事件失败", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "模拟FOUP移除事件失败");
            MessageBox.Show($"模拟FOUP移除事件失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private async void OnSimulatePurgeCompletedClick(object? sender, EventArgs e)
    {
        try
        {
            if (!_isConnected)
            {
                MessageBox.Show("请先连接到设备", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            uint portNumber = (uint)(_portNumberComboBox.SelectedIndex + 1);

            bool success = await _semiService.SimulatePurgeCompletedAsync(portNumber);

            if (success)
            {
                UpdatePortStatusDisplay(portNumber, "PURGE_COMPLETED", "");
                _logger.LogInformation($"模拟Port{portNumber} 充气完成事件成功");
                MessageBox.Show($"Port{portNumber} 充气完成事件已发送", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                MessageBox.Show("发送充气完成事件失败", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "模拟充气完成事件失败");
            MessageBox.Show($"模拟充气完成事件失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void UpdatePortStatusDisplay(uint portNumber, string status, string foupId)
    {
        if (InvokeRequired)
        {
            Invoke(() => UpdatePortStatusDisplay(portNumber, status, foupId));
            return;
        }

        var item = _portStatusListView.Items[(int)portNumber - 1];
        item.SubItems[1].Text = status;
        item.SubItems[2].Text = foupId;
        item.SubItems[7].Text = DateTime.Now.ToString("HH:mm:ss");
    }

    private string GetVariableName(uint variableId)
    {
        return variableId switch
        {
            1 => "状态",
            2 => "FOUP ID",
            3 => "温度",
            4 => "压力",
            5 => "流量",
            6 => "湿度",
            _ => $"变量{variableId}"
        };
    }

    #endregion

    #region 窗体事件

    private async void OnFormClosing(object? sender, FormClosingEventArgs e)
    {
        try
        {
            // 保存配置
            await _configManager.SaveConfigAsync(_config);

            // 断开连接
            if (_isConnected)
            {
                await _semiService.DisconnectAsync();
            }

            // 释放资源
            _semiService.Dispose();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "关闭窗体时发生错误");
        }
    }

    #endregion
}
