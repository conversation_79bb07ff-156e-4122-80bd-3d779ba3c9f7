# SEMI Protocol Tester - 快速启动指南

## 🚀 5分钟快速上手

### 第一步：启动程序
```bash
cd N2-STB
dotnet run --project SemiTester/SemiTester.csproj
```

### 第二步：配置连接
1. 在"连接设置"页面输入目标设备信息：
   - IP地址：`127.0.0.1`（本地测试）或实际设备IP
   - 端口：`5000`（或设备实际端口）
   - 设备ID：`1`
   - 主动连接模式：✅（推荐）

### 第三步：建立连接
1. 点击"连接"按钮
2. 观察连接状态变为"已连接"（绿色）
3. 状态栏显示"已连接"

### 第四步：测试通信
1. 切换到"消息测试"页面
2. 点击"Are You There"按钮测试设备响应
3. 点击"建立通信"建立SEMI协议通信
4. 点击"请求上线"使设备上线

### 第五步：查看日志
1. 切换到"消息日志"页面
2. 查看所有通信记录
3. 观察消息的发送和接收情况

## 🎯 常见使用场景

### 场景1：设备连接测试
**目标**：验证与SEMI设备的基本连接
**步骤**：
1. 配置设备IP和端口
2. 建立连接
3. 发送S1F1 (Are You There)
4. 确认收到S1F2回复

### 场景2：通信建立测试
**目标**：建立完整的SEMI/GEM通信
**步骤**：
1. 建立连接
2. 发送S1F13 (建立通信)
3. 确认收到S1F14回复
4. 通信状态变为"已建立"

### 场景3：设备上线测试
**目标**：使设备进入在线状态
**步骤**：
1. 建立通信
2. 发送S1F17 (请求上线)
3. 确认收到S1F18回复
4. 设备进入在线状态

### 场景4：自定义消息测试
**目标**：发送特定的SECS消息
**步骤**：
1. 选择Stream和Function编号
2. 输入消息数据（如需要）
3. 点击"发送消息"
4. 查看设备回复

## ⚡ 快捷键和技巧

### 界面操作
- `Ctrl+1`: 切换到连接设置页面
- `Ctrl+2`: 切换到消息测试页面
- `Ctrl+3`: 切换到消息日志页面
- `F5`: 刷新连接状态
- `Ctrl+L`: 清除日志

### 快速测试序列
1. **基础连接测试**：连接 → Are You There
2. **完整通信测试**：连接 → 建立通信 → 请求上线
3. **断线重连测试**：连接 → 断开 → 重新连接

## 🔧 常见问题快速解决

### Q: 连接失败怎么办？
**A**: 
1. 检查IP地址和端口是否正确
2. 确认目标设备已启动
3. 检查网络连通性：`ping [设备IP]`
4. 确认防火墙设置

### Q: 消息发送无响应？
**A**:
1. 确认连接状态为"已连接"
2. 检查设备是否支持该消息
3. 尝试发送Are You There测试
4. 查看日志中的错误信息

### Q: 日志显示乱码？
**A**:
1. 检查消息数据格式
2. 确认字符编码设置
3. 重启程序重新初始化

### Q: 程序运行缓慢？
**A**:
1. 清除历史日志
2. 减少最大日志条目数
3. 关闭不必要的日志记录

## 📋 测试检查清单

### 连接测试 ✅
- [ ] 程序启动正常
- [ ] 连接配置正确
- [ ] 连接建立成功
- [ ] 连接状态显示正确

### 消息测试 ✅
- [ ] Are You There 测试成功
- [ ] 建立通信成功
- [ ] 设备上线成功
- [ ] 自定义消息发送成功

### 日志功能 ✅
- [ ] 消息日志显示正常
- [ ] 日志颜色编码正确
- [ ] 日志导出功能正常
- [ ] 日志清除功能正常

### 配置管理 ✅
- [ ] 配置文件加载正常
- [ ] 配置修改保存成功
- [ ] 程序重启配置保持

## 🎨 界面说明图

```
┌─────────────────────────────────────────────────────────────┐
│ SEMI Protocol Tester                                    [_][□][×] │
├─────────────────────────────────────────────────────────────┤
│ [连接设置] [消息测试] [消息日志]                                │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  连接配置                    连接控制                        │
│  ┌─────────────────┐        ┌─────────────────┐             │
│  │ IP地址: 127.0.0.1│        │ [连接] [断开]    │             │
│  │ 端口:   5000     │        └─────────────────┘             │
│  │ 设备ID: 1        │                                        │
│  │ ☑ 主动连接模式    │        连接状态                        │
│  └─────────────────┘        ┌─────────────────┐             │
│                             │   已连接         │             │
│                             └─────────────────┘             │
├─────────────────────────────────────────────────────────────┤
│ 连接状态: 已连接                           消息: 15          │
└─────────────────────────────────────────────────────────────┘
```

## 📞 获取帮助

如需更详细的使用说明，请参考：
- 📖 [完整使用文档](README.md)
- 🔧 [配置文件说明](README.md#配置文件)
- 🐛 [故障排除指南](README.md#故障排除)

---

**提示**：首次使用建议先在本地环境（127.0.0.1）进行测试，熟悉界面和功能后再连接实际设备。
