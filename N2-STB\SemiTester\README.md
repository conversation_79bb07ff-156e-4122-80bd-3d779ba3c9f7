# SEMI Protocol Tester - 使用文档

## 概述

SEMI Protocol Tester 是一个专业的SEMI/GEM协议测试工具，基于您现有项目的SEMI协议功能重新设计和实现。该工具提供了完整的SEMI/GEM通信测试功能，包括连接管理、消息发送、状态监控和日志记录。

## 主要特性

### 🔗 连接管理
- 支持主动/被动连接模式
- 实时连接状态监控
- 自动重连机制
- 连接参数配置

### 📨 消息测试
- 支持所有SEMI标准消息
- 自定义消息发送
- 快捷操作按钮
- 消息格式验证

### 📊 实时监控
- 连接状态实时显示
- 通信状态监控
- 控制状态跟踪
- 消息统计

### 📝 日志系统
- 详细的消息日志
- 彩色分类显示
- 日志导出功能
- 历史记录管理

## 系统要求

- .NET 8.0 或更高版本
- Windows 10/11 操作系统
- 支持SEMI/GEM协议的设备或模拟器

## 安装和启动

### 编译项目
```bash
cd N2-STB
dotnet build SemiTester/SemiTester.csproj
```

### 运行程序
```bash
dotnet run --project SemiTester/SemiTester.csproj
```

或者直接运行编译后的可执行文件：
```bash
SemiTester\bin\Debug\net8.0-windows\SemiTester.exe
```

## 界面介绍

### 主界面布局
程序采用选项卡式界面设计，包含三个主要页面：

1. **连接设置** - 配置和管理SEMI连接
2. **消息测试** - 发送和测试SEMI消息
3. **消息日志** - 查看和管理通信日志

### 状态栏
- **连接状态**: 显示当前连接状态
- **消息计数**: 显示日志中的消息数量

## 详细使用说明

### 1. 连接设置页面

#### 连接配置组
- **IP地址**: 目标设备的IP地址（默认：127.0.0.1）
- **端口**: SEMI通信端口（默认：5000）
- **设备ID**: SEMI设备标识符（默认：1）
- **主动连接模式**: 
  - ✅ 选中：作为客户端主动连接到Host
  - ❌ 未选中：作为服务器等待Host连接

#### 连接控制组
- **连接按钮**: 建立SEMI连接
- **断开按钮**: 断开当前连接

#### 连接状态组
实时显示当前连接状态：
- **未连接**: 灰色显示，无连接
- **连接中**: 橙色显示，正在建立连接
- **已连接**: 绿色显示，连接成功
- **连接失败**: 红色显示，连接出现问题

### 2. 消息测试页面

#### 消息发送组
- **Stream**: 选择SECS消息流编号（1-10）
- **Function**: 选择SECS消息功能编号（1-50）
- **数据**: 输入消息数据内容（可选）
- **发送消息**: 发送自定义SECS消息

#### 快捷操作组
提供常用SEMI操作的快捷按钮：

- **建立通信 (S1F13)**: 发送通信建立请求
- **请求上线 (S1F17)**: 请求设备上线
- **请求下线 (S1F15)**: 请求设备下线  
- **Are You There (S1F1)**: 发送设备存在性测试

### 3. 消息日志页面

#### 工具栏
- **清除日志**: 清空所有日志记录
- **导出日志**: 将日志导出为JSON文件
- **自动滚动**: 自动滚动到最新日志条目

#### 日志列表
显示详细的通信日志，包含以下列：
- **时间**: 消息时间戳（精确到毫秒）
- **方向**: 消息方向（发送/接收/错误/信息）
- **消息**: SECS消息类型（如S1F1, S1F2等）
- **状态**: 操作状态（成功/失败）
- **内容**: 消息详细内容

#### 颜色编码
- 🔵 **蓝色**: 发送的消息
- 🟢 **绿色**: 接收的消息  
- 🔴 **红色**: 错误信息
- ⚫ **黑色**: 一般信息

## 配置文件

### 配置文件位置
```
SemiTester/Config/config.json
```

### 配置文件结构
```json
{
  "Connection": {
    "IpAddress": "127.0.0.1",
    "Port": 5000,
    "DeviceId": 1,
    "IsActive": true,
    "T3Timeout": 45000,
    "T5Timeout": 10000,
    "T6Timeout": 5000,
    "T7Timeout": 10000,
    "T8Timeout": 5000
  },
  "Device": {
    "MDLN": "SEMI_TESTER",
    "SoftRev": "V1.0.0",
    "Description": "SEMI Protocol Tester"
  },
  "UI": {
    "AutoScroll": true,
    "MaxLogEntries": 1000,
    "ShowTimestamp": true,
    "ShowMessageDetails": true,
    "Theme": "Default"
  },
  "Logging": {
    "EnableFileLogging": true,
    "LogDirectory": "Logs",
    "MaxLogFiles": 10,
    "MaxLogFileSize": 10485760
  }
}
```

### 配置参数说明

#### Connection 连接配置
- `IpAddress`: 目标IP地址
- `Port`: 通信端口
- `DeviceId`: 设备标识符
- `IsActive`: 是否主动连接模式
- `T3Timeout`: 回复超时时间（毫秒）
- `T5Timeout`: 连接分离时间（毫秒）
- `T6Timeout`: 控制事务超时（毫秒）
- `T7Timeout`: 未选择超时（毫秒）
- `T8Timeout`: 网络字符间超时（毫秒）

#### Device 设备信息
- `MDLN`: 设备型号名称
- `SoftRev`: 软件版本号
- `Description`: 设备描述

#### UI 界面配置
- `AutoScroll`: 是否自动滚动日志
- `MaxLogEntries`: 最大日志条目数
- `ShowTimestamp`: 是否显示时间戳
- `ShowMessageDetails`: 是否显示消息详情
- `Theme`: 界面主题

#### Logging 日志配置
- `EnableFileLogging`: 是否启用文件日志
- `LogDirectory`: 日志文件目录
- `MaxLogFiles`: 最大日志文件数
- `MaxLogFileSize`: 最大日志文件大小（字节）

## 常用操作流程

### 基本连接测试
1. 打开程序，切换到"连接设置"页面
2. 配置目标设备的IP地址和端口
3. 点击"连接"按钮建立连接
4. 观察连接状态变化
5. 连接成功后，切换到"消息测试"页面
6. 点击"Are You There"测试设备响应

### 建立SEMI通信
1. 确保设备已连接（连接状态显示为"已连接"）
2. 切换到"消息测试"页面
3. 点击"建立通信"按钮发送S1F13消息
4. 等待设备回复S1F14消息
5. 通信建立成功后可进行其他操作

### 设备上线流程
1. 建立通信成功后
2. 点击"请求上线"按钮发送S1F17消息
3. 等待设备回复S1F18消息
4. 上线成功后设备进入在线状态

### 自定义消息发送
1. 在"消息发送组"中选择Stream和Function
2. 在数据框中输入消息内容（可选）
3. 点击"发送消息"按钮
4. 在日志页面查看发送结果和设备回复

### 日志管理
1. 切换到"消息日志"页面查看通信记录
2. 使用"清除日志"清空历史记录
3. 使用"导出日志"保存日志到文件
4. 勾选"自动滚动"自动跟踪最新日志

## 故障排除

### 连接问题
**问题**: 无法连接到设备
**解决方案**:
1. 检查IP地址和端口配置是否正确
2. 确认目标设备已启动并监听指定端口
3. 检查网络连通性（ping测试）
4. 确认防火墙设置允许通信
5. 检查连接模式（主动/被动）是否匹配

**问题**: 连接建立后立即断开
**解决方案**:
1. 检查设备ID配置是否正确
2. 确认SEMI协议版本兼容性
3. 检查超时参数设置
4. 查看日志中的错误信息

### 消息发送问题
**问题**: 消息发送失败
**解决方案**:
1. 确认连接状态为"已连接"
2. 检查消息格式是否正确
3. 确认设备支持该消息类型
4. 检查消息数据格式

**问题**: 设备无响应
**解决方案**:
1. 检查设备是否处于正确状态
2. 确认消息是否需要回复
3. 检查超时设置
4. 尝试发送Are You There测试

### 日志问题
**问题**: 日志显示异常
**解决方案**:
1. 检查日志配置设置
2. 确认日志文件权限
3. 重启程序重新初始化
4. 检查磁盘空间

## 技术支持

如果遇到问题或需要技术支持，请：

1. 查看程序日志文件获取详细错误信息
2. 检查配置文件设置是否正确
3. 确认网络和设备状态
4. 记录问题复现步骤

## 版本历史

### V1.0.0 (当前版本)
- 基于现有项目SEMI协议功能重新设计
- 完整的连接管理功能
- 支持常用SEMI消息测试
- 实时日志记录和管理
- 可配置的界面和参数
- 专业的错误处理和状态监控

---

*本文档最后更新时间: 2024年10月29日*
